# Honeypot Bot Protection

This document describes the honeypot bot protection system implemented in the signup form to prevent automated bot submissions.

## Overview

The honeypot protection system uses multiple layers of bot detection:

1. **Hidden Honeypot Field**: A field that's invisible to users but visible to bots
2. **Timing Validation**: Checks if forms are submitted too quickly or too slowly
3. **Mouse Movement Detection**: Tracks human-like interaction patterns
4. **Server-side Validation**: Backend validation to prevent bypassing client-side checks

## How It Works

### Client-Side Protection

#### Honeypot Hook (`hooks/use-honeypot.ts`)
The `useHoneypot` hook provides:
- Hidden field management
- Timing validation (2 seconds minimum, 30 minutes maximum)
- Mouse movement tracking
- Form submission validation

#### Implementation in Forms
```tsx
import { useHoneypot } from '@/hooks/use-honeypot';

function MyForm() {
  const { 
    honeypotValue, 
    setHoneypotValue, 
    validateSubmission,
    resetHoneypot 
  } = useHoneypot();

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const validation = validateSubmission();
    if (!validation.isValid) {
      console.log('Bot detected:', validation.reason);
      return;
    }
    
    // Proceed with form submission
  };
}
```

#### Hidden Field Styling
The honeypot field uses CSS to be invisible to users:
```css
.honeypot-field {
  position: absolute;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
```

### Server-Side Protection

#### Backend Validation (`netlify/functions/form-submit.ts`)
The server validates:
- Honeypot field is empty
- Form timing is within acceptable range
- Removes honeypot data before sending to external APIs

```typescript
// Honeypot validation
if (fields.website && fields.website.trim() !== '') {
  return { success: false, error: 'Invalid submission detected.' };
}

// Timing validation
const timeTaken = currentTime - startTime;
if (timeTaken < 2000) {
  return { success: false, error: 'Invalid submission detected.' };
}
```

## Detection Methods

### 1. Honeypot Field
- **Field Name**: `website`
- **Purpose**: Bots often auto-fill all form fields
- **Detection**: If field contains any value, mark as bot
- **User Impact**: None (field is hidden)

### 2. Timing Validation
- **Too Fast**: < 2 seconds (likely automated)
- **Too Slow**: > 30 minutes (session expired)
- **Purpose**: Humans need time to read and fill forms
- **User Impact**: Minimal (most users take 5-30 seconds)

### 3. Mouse Movement
- **Purpose**: Detect human-like interaction
- **Implementation**: Track if mouse has moved during session
- **Note**: Currently tracked but not used for blocking (accessibility concerns)

## Configuration

### Timing Thresholds
```typescript
const MIN_FORM_TIME = 2000; // 2 seconds
const MAX_FORM_TIME = 30 * 60 * 1000; // 30 minutes
```

### Honeypot Field Settings
```typescript
const HONEYPOT_FIELD_NAME = 'website';
const HONEYPOT_LABEL = 'Website (leave this field empty)';
```

## Testing

### Running Tests
```bash
npm test honeypot.test.tsx
```

### Manual Testing
1. **Normal User**: Fill form normally → Should work
2. **Bot Simulation**: Fill honeypot field → Should be blocked
3. **Speed Test**: Submit immediately → Should be blocked
4. **Timeout Test**: Wait 31+ minutes → Should be blocked

### Test Cases Covered
- ✅ Empty honeypot validation
- ✅ Filled honeypot detection
- ✅ Timing validation (too fast/slow)
- ✅ State reset functionality
- ✅ Edge cases (whitespace, etc.)

## Cloudflare Integration

This honeypot system **complements** Cloudflare bot protection:

- **Cloudflare**: Blocks sophisticated bots, DDoS, malicious IPs
- **Honeypot**: Catches simple scrapers, form auto-fillers, basic bots

Both systems work together for comprehensive protection.

## Accessibility Considerations

- Honeypot field uses `aria-hidden="true"`
- Field is positioned off-screen (not `display: none`)
- No impact on screen readers or keyboard navigation
- Timing allows for assistive technology users

## Monitoring & Analytics

### Server Logs
Bot detection events are logged with reasons:
```
Bot detected via honeypot field: spam-content
Bot detected: form submitted too quickly 150ms
Bot detected: form session expired 1800000ms
```

### Metrics to Track
- Bot detection rate by method
- False positive rate
- Form completion time distribution
- User experience impact

## Maintenance

### Regular Reviews
- Monitor false positive rates
- Adjust timing thresholds if needed
- Update honeypot field names periodically
- Review bot detection logs

### Updates
- Keep honeypot field names unpredictable
- Consider rotating field names monthly
- Monitor for new bot patterns
- Update validation rules as needed

## Security Notes

- Never expose honeypot logic in client-side code comments
- Use server-side validation as the final authority
- Log but don't expose specific detection methods to users
- Consider rate limiting for additional protection

## Troubleshooting

### Common Issues
1. **False Positives**: User flagged as bot
   - Check timing thresholds
   - Verify honeypot field isn't visible
   - Review accessibility tools interaction

2. **Bots Getting Through**: Submissions still automated
   - Check server-side validation
   - Consider additional detection methods
   - Review Cloudflare settings

3. **Performance Issues**: Slow form interactions
   - Optimize mouse movement tracking
   - Review timing validation logic
   - Check for memory leaks in hooks
